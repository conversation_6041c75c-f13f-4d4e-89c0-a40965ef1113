#!/bin/bash

# 企业微信群机器人自动发送消息服务部署脚本

set -e

echo "开始部署企业微信群机器人服务..."

# 创建部署目录
DEPLOY_DIR="/opt/wechat-robot"
sudo mkdir -p $DEPLOY_DIR

# 复制文件
echo "复制程序文件..."
sudo cp send_message.py $DEPLOY_DIR/
sudo cp requirements.txt $DEPLOY_DIR/

# 设置权限
sudo chown -R www-data:www-data $DEPLOY_DIR

# 安装 Python 依赖
echo "安装 Python 依赖..."
sudo pip3 install -r $DEPLOY_DIR/requirements.txt

# 复制 systemd 服务文件
echo "设置系统服务..."
sudo cp send_message.service /etc/systemd/system/

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable send_message.service
sudo systemctl start send_message.service

echo "部署完成！"
echo ""
echo "服务管理命令："
echo "查看状态: sudo systemctl status send_message.service"
echo "停止服务: sudo systemctl stop send_message.service"
echo "启动服务: sudo systemctl start send_message.service"
echo "重启服务: sudo systemctl restart send_message.service"
echo "查看日志: sudo journalctl -u send_message.service -f"
echo ""
echo "重要提醒："
echo "1. 请修改 $DEPLOY_DIR/send_message.py 中的 WEBHOOK_URL"
echo "2. 将 YOUR_WEBHOOK_KEY 替换为实际的机器人 webhook key"
echo "3. 修改完成后运行: sudo systemctl restart send_message.service"
