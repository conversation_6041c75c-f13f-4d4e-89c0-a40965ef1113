#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本 - 立即发送一条测试消息
用于验证 webhook 配置是否正确
"""

import requests
import json
from datetime import datetime
import pytz

# 从主程序导入配置（需要先修改 send_message.py 中的 WEBHOOK_URL）
try:
    from send_message import WEBHOOK_URL, MESSAGE_CONTENT, should_send_this_week, get_week_key, load_send_record
except ImportError:
    print("错误：无法导入配置，请确保 send_message.py 在同一目录下")
    exit(1)

def test_send_message():
    """发送测试消息"""
    print("开始发送测试消息...")
    print(f"Webhook URL: {WEBHOOK_URL[:50]}...")  # 只显示前50个字符以保护隐私
    
    # 添加测试标识的消息
    test_message = f"🧪 **测试消息** - {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}\n\n" + MESSAGE_CONTENT
    
    payload = {
        "msgtype": "text",
        "text": {
            "content": test_message
        }
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=payload, timeout=10)
        response_data = response.json()
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200 and response_data.get("errcode") == 0:
            print("✅ 测试消息发送成功！")
            return True
        else:
            print("❌ 测试消息发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_configuration():
    """检查配置"""
    print("检查配置...")
    
    if "YOUR_WEBHOOK_KEY" in WEBHOOK_URL:
        print("❌ 错误：请先修改 send_message.py 中的 WEBHOOK_URL")
        print("   将 YOUR_WEBHOOK_KEY 替换为实际的机器人 webhook key")
        return False
    
    if not WEBHOOK_URL.startswith("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key="):
        print("❌ 错误：Webhook URL 格式不正确")
        return False
    
    print("✅ 配置检查通过")
    return True

def check_send_status():
    """检查发送状态"""
    print("\n检查发送状态...")
    beijing_tz = pytz.timezone("Asia/Shanghai")
    beijing_now = datetime.now(beijing_tz)
    week_key = get_week_key(beijing_now)
    
    print(f"当前北京时间: {beijing_now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前周标识: {week_key}")
    
    if should_send_this_week():
        print("✅ 本周尚未发送消息")
    else:
        record = load_send_record()
        if week_key in record:
            sent_time = record[week_key]['sent_time']
            print(f"❌ 本周已发送消息，发送时间: {sent_time}")
        else:
            print("❌ 发送记录异常")
    
    # 显示发送记录
    record = load_send_record()
    if record:
        print("\n📝 最近发送记录:")
        for week, info in sorted(record.items(), reverse=True)[:3]:
            print(f"  {week}: {info['sent_time']}")
    else:
        print("\n📝 暂无发送记录")

if __name__ == "__main__":
    print("=" * 60)
    print("企业微信群机器人测试工具 (随机时间版本)")
    print("=" * 60)
    
    check_send_status()
    
    if check_configuration():
        print("\n" + "-" * 40)
        success = test_send_message()
        print("-" * 40)
        
        if success:
            print("\n🎉 测试完成！如果群里收到了测试消息，说明配置正确。")
            print("📝 现在可以部署服务，等待定时发送功能生效。")
            print("⏰ 发送时间：每周一北京时间 00:01-00:10 随机时间")
            print("🔒 防重复：每周最多发送一次消息")
        else:
            print("\n🔧 测试失败，请检查：")
            print("1. Webhook URL 是否正确")
            print("2. 机器人是否已添加到群聊")
            print("3. 网络连接是否正常")
    else:
        print("\n❌ 配置检查失败，请先修改配置后再测试")
