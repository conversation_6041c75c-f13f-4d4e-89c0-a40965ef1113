[Unit]
Description=WeChat Work Robot Auto Send Message Service
After=network.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/wechat-robot/send_message.py
WorkingDirectory=/opt/wechat-robot/
Restart=always
RestartSec=10
StandardOutput=append:/var/log/send_message.log
StandardError=append:/var/log/send_message_error.log
Environment=PYTHONUNBUFFERED=1
Environment=TZ=Asia/Shanghai
User=www-data

[Install]
WantedBy=multi-user.target
