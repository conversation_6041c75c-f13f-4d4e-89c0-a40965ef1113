import requests
import time
import logging
import random
import json
from datetime import datetime, timedelta
import pytz
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('send_message.log'),
        logging.StreamHandler()
    ]
)

# 企业微信群机器人 Webhook URL
WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1eb9e273-1ecd-40af-8748-087f1d2e16fb"

# 消息内容
MESSAGE_CONTENT = """车型HT——LC0DD4C44R7002467——罗基——场地皆可（广场>直线）——时间段皆可"""

# 定义北京时间时区
BEIJING_TZ = pytz.timezone("Asia/Shanghai")

# 发送记录文件
SEND_RECORD_FILE = "send_record.json"

def load_send_record():
    """加载发送记录"""
    try:
        with open(SEND_RECORD_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_send_record(record):
    """保存发送记录"""
    with open(SEND_RECORD_FILE, 'w', encoding='utf-8') as f:
        json.dump(record, f, ensure_ascii=False, indent=2)

def get_week_key(beijing_time):
    """获取周标识（年-周数）"""
    year, week, _ = beijing_time.isocalendar()
    return f"{year}-W{week:02d}"

def get_random_send_time():
    """获取或生成本周的随机发送时间（分钟）"""
    week_key = get_week_key(datetime.now(BEIJING_TZ))
    record = load_send_record()
    
    # 如果本周已经有随机时间，直接返回
    if week_key in record and 'random_minute' in record[week_key]:
        return record[week_key]['random_minute']
    
    # 生成新的随机分钟（0,1分钟）
    random_minute = random.choice([0, 1])
    
    # 保存随机时间到记录中
    if week_key not in record:
        record[week_key] = {}
    record[week_key]['random_minute'] = random_minute
    save_send_record(record)
    
    logging.info(f"为本周({week_key})生成随机发送时间: 00:{random_minute:02d}:30")
    return random_minute

def should_send_this_week():
    """检查本周是否已经发送过消息"""
    beijing_now = datetime.now(BEIJING_TZ)
    week_key = get_week_key(beijing_now)
    
    record = load_send_record()
    return week_key not in record or 'sent_time' not in record.get(week_key, {})

def mark_sent_this_week():
    """标记本周已发送"""
    beijing_now = datetime.now(BEIJING_TZ)
    week_key = get_week_key(beijing_now)
    
    record = load_send_record()
    if week_key not in record:
        record[week_key] = {}
    
    record[week_key]['sent_time'] = beijing_now.strftime('%Y-%m-%d %H:%M:%S')
    record[week_key]['timestamp'] = beijing_now.timestamp()
    
    # 清理超过4周的旧记录，避免文件过大
    cutoff_time = beijing_now - timedelta(weeks=4)
    cutoff_week = get_week_key(cutoff_time)
    
    keys_to_remove = []
    for key in record.keys():
        if key < cutoff_week:
            keys_to_remove.append(key)
    
    for key in keys_to_remove:
        del record[key]
    
    save_send_record(record)
    logging.info(f"已标记本周({week_key})为已发送")

def send_message():
    """发送消息到企业微信群，带重试机制"""
    payload = {
        "msgtype": "text",
        "text": {
            "content": MESSAGE_CONTENT
        }
    }
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(WEBHOOK_URL, json=payload, timeout=10)
            response_data = response.json()
            
            if response.status_code == 200 and response_data.get("errcode") == 0:
                logging.info("消息发送成功")
                mark_sent_this_week()  # 标记本周已发送
                return True
            else:
                logging.error(f"消息发送失败 (尝试 {attempt + 1}/{max_retries}): {response_data}")
        except Exception as e:
            logging.error(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
        
        if attempt < max_retries - 1:
            time.sleep(5)  # 等待5秒后重试
    
    logging.error("所有重试均失败，消息发送失败")
    return False

def check_and_send_randomly():
    """检查是否为周一随机时间，且本周未发送过"""
    beijing_now = datetime.now(BEIJING_TZ)
    
    # 检查是否为周一
    if beijing_now.weekday() != 0:
        return False
    
    # 检查是否在00:00-00:01时间窗口内（包含0分钟的边界情况）
    if not (beijing_now.hour == 0 and 0 <= beijing_now.minute <= 1):
        return False
    
    # 检查本周是否已经发送过
    if not should_send_this_week():
        logging.info(f"本周已发送过消息，跳过发送")
        return False
    
    # 获取本周的随机发送分钟
    random_minute = get_random_send_time()
    
    # 检查是否为目标时间（随机分钟，30秒，允许±15秒误差）
    if beijing_now.minute == random_minute and 15 <= beijing_now.second <= 45:
        logging.info(f"触发随机定时发送 - 北京时间: {beijing_now.strftime('%Y-%m-%d %H:%M:%S')} (目标时间: 00:{random_minute:02d}:30)")
        send_message()
        return True
    
    return False

if __name__ == "__main__":
    logging.info("定时任务服务已启动...")
    logging.info(f"当前北京时间: {datetime.now(BEIJING_TZ).strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("发送时间：每周一北京时间 00:00-00:01 随机分钟，固定30秒")
    
    # 检查配置
    if "YOUR_WEBHOOK_KEY" in WEBHOOK_URL:
        logging.warning("警告: 请修改 WEBHOOK_URL 中的 YOUR_WEBHOOK_KEY 为实际的机器人key")
    
    # 显示本周发送状态和随机时间
    beijing_now = datetime.now(BEIJING_TZ)
    week_key = get_week_key(beijing_now)
    
    if should_send_this_week():
        random_minute = get_random_send_time()
        logging.info(f"本周尚未发送消息，随机发送时间: 周一 00:{random_minute:02d}:30 (±15秒)")
    else:
        record = load_send_record()
        if week_key in record and 'sent_time' in record[week_key]:
            sent_time = record[week_key]['sent_time']
            logging.info(f"本周已发送消息，发送时间: {sent_time}")
    
    while True:
        # 设置系统时区为北京时间来确保时间正确
        os.environ['TZ'] = 'Asia/Shanghai'
        if hasattr(time, 'tzset'):
            time.tzset()
        
        # 检查是否需要发送消息
        check_and_send_randomly()
        
        time.sleep(10)  # 每10秒检查一次
