# 🚀 企业微信群机器人更新部署清单

## ✅ 修改完成确认

### 1. 消息内容修改 ✅
- **原内容**: VSE团队试车场地预约请求（多行复杂格式）
- **新内容**: `车型HT——LC0DD4C44R7002467——罗基——场地皆可（广场>直线）——时间段皆可`

### 2. 发送时间调整 ✅
- **原设置**: 每周一 00:00-00:03 分钟随机选择（0,1,2,3分钟）
- **新设置**: 每周一 00:00-00:01 分钟随机选择（0,1分钟）

### 3. 代码优化 ✅
- 移除了未使用的 `schedule` 模块导入
- 所有语法检查通过

## 📦 需要上传到服务器的文件

```
send_message.py          # 主程序文件（已修改）
update_deploy.sh         # 更新部署脚本
verify_changes.py        # 验证脚本（可选）
更新部署说明.md          # 部署说明文档
```

## 🔧 服务器部署步骤

### 第一步：上传文件
将修改后的文件上传到服务器的项目目录

### 第二步：执行更新部署
```bash
# 1. 给脚本执行权限
chmod +x update_deploy.sh

# 2. 执行更新部署
sudo ./update_deploy.sh
```

### 第三步：验证部署
```bash
# 检查服务状态
sudo systemctl status send_message.service

# 查看实时日志
sudo journalctl -u send_message.service -f

# 测试发送功能（可选）
cd /opt/wechat-robot
sudo python3 test_send.py
```

## 📋 预期结果

### 服务状态
- ✅ 服务正常运行
- ✅ 日志显示新的时间窗口设置
- ✅ 随机时间重新生成

### 下次发送
- **时间**: 下周一北京时间 00:00 或 00:01 的随机时间
- **内容**: 车型HT——LC0DD4C44R7002467——罗基——场地皆可（广场>直线）——时间段皆可

## 🔍 故障排除

如果遇到问题，请按以下顺序检查：

1. **服务状态检查**
   ```bash
   sudo systemctl status send_message.service
   ```

2. **查看错误日志**
   ```bash
   sudo journalctl -u send_message.service -n 20
   ```

3. **检查文件权限**
   ```bash
   ls -la /opt/wechat-robot/
   ```

4. **手动测试**
   ```bash
   cd /opt/wechat-robot
   sudo python3 -c "from send_message import MESSAGE_CONTENT; print(MESSAGE_CONTENT)"
   ```

## 📞 联系支持

如果部署过程中遇到任何问题，请提供：
- 错误日志输出
- 服务状态信息
- 执行的具体命令

---

**重要提醒**: 更新后本周的随机发送时间会重新生成，下次发送将在下周一的00:00或00:01分钟进行。
