#!/bin/bash

# 企业微信群机器人更新部署脚本
# 用于更新已部署的服务

set -e

echo "开始更新企业微信群机器人服务..."

# 部署目录
DEPLOY_DIR="/opt/wechat-robot"

# 检查服务是否存在
if ! systemctl list-unit-files | grep -q "send_message.service"; then
    echo "错误: 服务尚未部署，请先运行初始部署脚本"
    exit 1
fi

# 停止服务
echo "停止服务..."
sudo systemctl stop send_message.service

# 备份当前配置
echo "备份当前配置..."
sudo cp $DEPLOY_DIR/send_message.py $DEPLOY_DIR/send_message.py.bak.$(date +%Y%m%d_%H%M%S)

# 更新程序文件
echo "更新程序文件..."
sudo cp send_message.py $DEPLOY_DIR/
sudo cp requirements.txt $DEPLOY_DIR/

# 如果有新的工具文件，也更新
if [ -f "test_send.py" ]; then
    sudo cp test_send.py $DEPLOY_DIR/
fi

if [ -f "manage_records.py" ]; then
    sudo cp manage_records.py $DEPLOY_DIR/
fi

# 设置权限
sudo chown -R www-data:www-data $DEPLOY_DIR

# 更新 Python 依赖（如果有新依赖）
echo "检查并更新 Python 依赖..."
sudo pip3 install -r $DEPLOY_DIR/requirements.txt

# 重新启动服务
echo "重新启动服务..."
sudo systemctl start send_message.service

# 检查服务状态
echo "检查服务状态..."
sleep 2
if systemctl is-active --quiet send_message.service; then
    echo "✅ 服务更新成功并正在运行"
    echo ""
    echo "查看服务状态: sudo systemctl status send_message.service"
    echo "查看实时日志: sudo journalctl -u send_message.service -f"
    echo "查看程序日志: sudo tail -f $DEPLOY_DIR/send_message.log"
    echo ""
    echo "⚠️  重要提醒："
    echo "1. 随机发送时间已更改为 00:00-00:03 分钟"
    echo "2. 本周的随机时间会重新生成"
    echo "3. 如需立即测试，可运行: cd $DEPLOY_DIR && sudo python3 test_send.py"
else
    echo "❌ 服务启动失败，请检查日志"
    echo "查看错误日志: sudo journalctl -u send_message.service -n 20"
    exit 1
fi
