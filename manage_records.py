#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
发送记录管理工具
用于查看、清理或重置发送记录
"""

import json
import os
from datetime import datetime
import pytz

SEND_RECORD_FILE = "send_record.json"
BEIJING_TZ = pytz.timezone("Asia/Shanghai")

def load_send_record():
    """加载发送记录"""
    try:
        with open(SEND_RECORD_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_send_record(record):
    """保存发送记录"""
    with open(SEND_RECORD_FILE, 'w', encoding='utf-8') as f:
        json.dump(record, f, ensure_ascii=False, indent=2)

def get_week_key(beijing_time):
    """获取周标识（年-周数）"""
    year, week, _ = beijing_time.isocalendar()
    return f"{year}-W{week:02d}"

def show_records():
    """显示发送记录"""
    record = load_send_record()
    if not record:
        print("📝 暂无发送记录")
        return
    
    print("📝 发送记录:")
    print("-" * 50)
    for week, info in sorted(record.items(), reverse=True):
        print(f"📅 {week}: {info['sent_time']}")
    print("-" * 50)

def clear_current_week():
    """清除本周的发送记录"""
    beijing_now = datetime.now(BEIJING_TZ)
    current_week = get_week_key(beijing_now)
    
    record = load_send_record()
    if current_week in record:
        del record[current_week]
        save_send_record(record)
        print(f"✅ 已清除本周({current_week})的发送记录")
        print("💡 现在可以重新发送本周的消息了")
    else:
        print(f"ℹ️  本周({current_week})没有发送记录")

def clear_all_records():
    """清除所有发送记录"""
    confirm = input("⚠️  确定要清除所有发送记录吗？(输入 'yes' 确认): ")
    if confirm.lower() == 'yes':
        save_send_record({})
        print("✅ 已清除所有发送记录")
    else:
        print("❌ 操作已取消")

def main():
    print("=" * 60)
    print("企业微信群机器人 - 发送记录管理工具")
    print("=" * 60)
    
    beijing_now = datetime.now(BEIJING_TZ)
    current_week = get_week_key(beijing_now)
    print(f"📅 当前北京时间: {beijing_now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📅 当前周标识: {current_week}")
    print()
    
    while True:
        print("请选择操作:")
        print("1. 查看发送记录")
        print("2. 清除本周发送记录")
        print("3. 清除所有发送记录")
        print("4. 退出")
        print()
        
        choice = input("请输入选项 (1-4): ").strip()
        
        if choice == '1':
            print()
            show_records()
            print()
        elif choice == '2':
            print()
            clear_current_week()
            print()
        elif choice == '3':
            print()
            clear_all_records()
            print()
        elif choice == '4':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选项，请重新选择")
            print()

if __name__ == "__main__":
    main()
