# 企业微信群机器人服务更新说明

## 本次修改内容

### 1. 消息内容更新
**原内容：**
```
VSE团队试车场地预约请求

车型：HT
联系人电话：19065003368

预约需求：
优先预约最早可用日期，并在该日期上按以下优先级安排：
时段优先级：13:00–14:00 > 14:00–15:00 > 15:00–16:00 > 16:00–17:00
场地优先级：包场 > 广场 > 直线

麻烦李工帮忙安排一下，感谢支持！
```

**新内容：**
```
车型HT——LC0DD4C44R7002467——罗基——场地皆可（广场>直线）——时间段皆可
```

### 2. 发送时间调整
- **原设置：** 每周一 00:00-00:03 分钟随机选择（0,1,2,3分钟）
- **新设置：** 每周一 00:00-00:01 分钟随机选择（0,1分钟）

## 部署到服务器的步骤

### 方法一：使用更新脚本（推荐）

1. **上传修改后的文件到服务器**
   ```bash
   # 将以下文件上传到服务器的项目目录
   - send_message.py (已修改)
   - update_deploy.sh (更新脚本)
   ```

2. **执行更新部署**
   ```bash
   # 给脚本执行权限
   chmod +x update_deploy.sh
   
   # 执行更新
   sudo ./update_deploy.sh
   ```

### 方法二：手动更新

1. **停止服务**
   ```bash
   sudo systemctl stop send_message.service
   ```

2. **备份当前文件**
   ```bash
   sudo cp /opt/wechat-robot/send_message.py /opt/wechat-robot/send_message.py.backup.$(date +%Y%m%d_%H%M%S)
   ```

3. **更新文件**
   ```bash
   sudo cp send_message.py /opt/wechat-robot/
   sudo chown www-data:www-data /opt/wechat-robot/send_message.py
   ```

4. **重启服务**
   ```bash
   sudo systemctl start send_message.service
   ```

## 验证更新

### 检查服务状态
```bash
sudo systemctl status send_message.service
```

### 查看实时日志
```bash
sudo journalctl -u send_message.service -f
```

### 查看程序日志
```bash
sudo tail -f /opt/wechat-robot/send_message.log
```

### 测试发送功能
```bash
cd /opt/wechat-robot
sudo python3 test_send.py
```

## 重要提醒

1. **新的随机时间生成**：更新后，本周的随机发送时间会重新生成
2. **发送时间窗口缩小**：现在只在00:00或00:01分钟发送，更加精确
3. **消息内容简化**：新的消息内容更加简洁明了
4. **下次发送时间**：下周一北京时间 00:00 或 00:01 的随机时间

## 故障排除

如果服务启动失败，请检查：

1. **查看详细错误日志**
   ```bash
   sudo journalctl -u send_message.service -n 50
   ```

2. **检查文件权限**
   ```bash
   ls -la /opt/wechat-robot/
   ```

3. **手动测试Python脚本**
   ```bash
   cd /opt/wechat-robot
   sudo python3 -c "from send_message import *; print('Import OK')"
   ```

4. **重新部署（如果需要）**
   ```bash
   sudo ./update_deploy.sh
   ```
