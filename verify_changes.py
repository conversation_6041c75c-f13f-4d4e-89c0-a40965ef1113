#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证修改是否正确的脚本
"""

import sys
import os

def verify_message_content():
    """验证消息内容是否正确"""
    try:
        from send_message import MESSAGE_CONTENT
        expected_content = "车型HT——LC0DD4C44R7002467——罗基——场地皆可（广场>直线）——时间段皆可"
        
        if MESSAGE_CONTENT.strip() == expected_content:
            print("✅ 消息内容修改正确")
            print(f"   内容: {MESSAGE_CONTENT}")
            return True
        else:
            print("❌ 消息内容修改错误")
            print(f"   期望: {expected_content}")
            print(f"   实际: {MESSAGE_CONTENT}")
            return False
    except Exception as e:
        print(f"❌ 无法验证消息内容: {e}")
        return False

def verify_random_time_range():
    """验证随机时间范围是否正确"""
    try:
        import random
        # 模拟随机时间生成逻辑
        random_choices = [0, 1]  # 期望的选择范围
        
        # 检查代码中的随机选择
        with open('send_message.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "random.choice([0, 1])" in content:
            print("✅ 随机时间范围修改正确 (0,1分钟)")
            return True
        else:
            print("❌ 随机时间范围修改错误")
            return False
    except Exception as e:
        print(f"❌ 无法验证随机时间范围: {e}")
        return False

def verify_time_window():
    """验证时间窗口是否正确"""
    try:
        with open('send_message.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "0 <= beijing_now.minute <= 1" in content:
            print("✅ 时间窗口修改正确 (00:00-00:01)")
            return True
        else:
            print("❌ 时间窗口修改错误")
            return False
    except Exception as e:
        print(f"❌ 无法验证时间窗口: {e}")
        return False

def verify_imports():
    """验证导入是否正常"""
    try:
        import requests
        import pytz
        from datetime import datetime
        print("✅ 依赖包导入正常")
        return True
    except ImportError as e:
        print(f"❌ 依赖包导入失败: {e}")
        return False

def main():
    print("=" * 60)
    print("企业微信群机器人修改验证")
    print("=" * 60)
    
    all_passed = True
    
    # 验证各项修改
    checks = [
        ("依赖包检查", verify_imports),
        ("消息内容检查", verify_message_content),
        ("随机时间范围检查", verify_random_time_range),
        ("时间窗口检查", verify_time_window),
    ]
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}:")
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有修改验证通过！")
        print("\n📋 修改摘要:")
        print("1. ✅ 消息内容已更新为简化版本")
        print("2. ✅ 发送时间调整为0,1分钟随机")
        print("3. ✅ 时间窗口缩小为00:00-00:01")
        print("\n🚀 可以部署到服务器了！")
        print("   运行: chmod +x update_deploy.sh && sudo ./update_deploy.sh")
    else:
        print("❌ 验证失败，请检查修改！")
        sys.exit(1)

if __name__ == "__main__":
    main()
