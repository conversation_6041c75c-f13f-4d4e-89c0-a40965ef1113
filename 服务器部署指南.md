# 企业微信群机器人服务器部署指南

## 📁 需要上传的文件

你需要将以下文件上传到服务器：

**必需文件：**
- `send_message.py` - 主程序
- `requirements.txt` - 依赖包列表
- `send_message.service` - 系统服务配置
- `deploy.sh` - 一键部署脚本

**工具文件（可选但推荐）：**
- `test_send.py` - 测试工具
- `manage_records.py` - 记录管理工具
- `操作步骤.txt` - 说明文档

## 🚀 部署步骤

### 方法一：使用一键部署脚本（推荐）

1. **上传文件到服务器**
   ```bash
   # 方式1：使用 scp 命令
   scp send_message.py requirements.txt send_message.service deploy.sh test_send.py manage_records.py root@139.159.198.126:/tmp/
   
   # 方式2：使用 SFTP 工具（如 WinSCP、FileZilla）
   # 将所有文件上传到服务器的 /tmp/ 目录
   ```

2. **连接到服务器**
   ```bash
   ssh root@139.159.198.126
   ```

3. **执行一键部署**
   ```bash
   cd /tmp
   ls -la  # 确认文件已上传
   
   # 给部署脚本执行权限
   chmod +x deploy.sh
   
   # 执行一键部署
   sudo ./deploy.sh
   ```

### 方法二：手动部署

如果自动部署脚本有问题，可以手动执行：

1. **创建部署目录**
   ```bash
   sudo mkdir -p /opt/wechat-robot
   ```

2. **复制文件**
   ```bash
   sudo cp /tmp/send_message.py /opt/wechat-robot/
   sudo cp /tmp/requirements.txt /opt/wechat-robot/
   sudo cp /tmp/test_send.py /opt/wechat-robot/
   sudo cp /tmp/manage_records.py /opt/wechat-robot/
   ```

3. **安装依赖**
   ```bash
   sudo pip3 install -r /opt/wechat-robot/requirements.txt
   ```

4. **设置权限**
   ```bash
   sudo chown -R www-data:www-data /opt/wechat-robot
   ```

5. **配置系统服务**
   ```bash
   sudo cp /tmp/send_message.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable send_message.service
   sudo systemctl start send_message.service
   ```

## ⚙️ 重要配置

**在启动服务前，必须修改 webhook 地址：**

```bash
# 编辑配置文件
sudo nano /opt/wechat-robot/send_message.py

# 找到这行：
WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1eb9e273-1ecd-40af-8748-087f1d2e16fb"

# 确认这个key是否正确，如果不是你的实际key，请修改
```

## 🔍 验证部署

1. **测试配置**
   ```bash
   cd /opt/wechat-robot
   sudo python3 test_send.py
   ```

2. **检查服务状态**
   ```bash
   sudo systemctl status send_message.service
   ```

3. **查看日志**
   ```bash
   # 查看实时日志
   sudo journalctl -u send_message.service -f
   
   # 查看程序日志
   sudo tail -f /opt/wechat-robot/send_message.log
   ```

## 📂 最终文件布局

部署完成后，服务器上的文件结构：

```
/opt/wechat-robot/                    # 主程序目录
├── send_message.py                   # 主程序
├── requirements.txt                  # 依赖包
├── test_send.py                      # 测试工具
├── manage_records.py                 # 管理工具
├── send_message.log                  # 程序日志（自动生成）
└── send_record.json                  # 发送记录（自动生成）

/etc/systemd/system/
└── send_message.service              # 系统服务配置

/var/log/
├── send_message.log                  # 系统输出日志
└── send_message_error.log            # 系统错误日志
```

## 🎯 服务管理命令

```bash
# 查看服务状态
sudo systemctl status send_message.service

# 启动服务
sudo systemctl start send_message.service

# 停止服务
sudo systemctl stop send_message.service

# 重启服务
sudo systemctl restart send_message.service

# 查看本周发送状态
cd /opt/wechat-robot && sudo python3 test_send.py

# 管理发送记录
cd /opt/wechat-robot && sudo python3 manage_records.py
```

## 🚨 部署后检查清单

- [ ] 文件已上传到服务器
- [ ] Webhook URL 配置正确
- [ ] 服务已启动并运行正常
- [ ] 测试脚本执行成功
- [ ] 能看到本周的随机发送时间
- [ ] 日志文件正常生成

## 📝 注意事项

1. **服务器要求**
   - Linux 系统（推荐 Ubuntu/CentOS）
   - Python 3.6+ 
   - systemd 支持
   - 网络能访问企业微信API

2. **安全提醒**
   - 保护好 webhook 地址，不要泄漏
   - 定期检查服务运行状态
   - 备份配置文件和发送记录

3. **程序特性**
   - 每周一随机时间发送（00:01-00:10中的随机分钟，30秒±15秒）
   - 每周最多发送一次，防止重复
   - 自动重试机制，提高发送可靠性
   - 完整的日志记录，便于问题排查

按照这些步骤，你就可以成功将程序部署到服务器 139.159.198.126 上，程序会在每周一的随机时间自动发送消息。

## 🔄 更新已部署的服务

如果需要更新已部署的服务（比如修改随机时间范围），使用以下方法：

### 方法一：使用更新脚本（推荐）

1. **上传更新的文件**
   ```bash
   # 上传修改后的文件
   scp send_message.py requirements.txt update_deploy.sh loki@139.159.198.126:/tmp/
   ```

2. **执行更新部署**
   ```bash
   ssh loki@139.159.198.126
   cd /tmp
   chmod +x update_deploy.sh
   sudo ./update_deploy.sh
   ```

### 方法二：手动更新

1. **停止服务**
   ```bash
   sudo systemctl stop send_message.service
   ```

2. **备份并更新文件**
   ```bash
   # 备份当前文件
   sudo cp /opt/wechat-robot/send_message.py /opt/wechat-robot/send_message.py.bak
   
   # 更新文件
   sudo cp /tmp/send_message.py /opt/wechat-robot/
   sudo chown www-data:www-data /opt/wechat-robot/send_message.py
   ```

3. **重启服务**
   ```bash
   sudo systemctl start send_message.service
   sudo systemctl status send_message.service
   ```
