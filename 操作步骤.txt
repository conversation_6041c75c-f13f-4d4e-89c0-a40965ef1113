# 企业微信群机器人自动发送消息服务 - 部署指南

## 快速部署

1. **上传文件到服务器**
   将所有文件上传到服务器任意目录（如 `/tmp/wechat-robot/`）

2. **修改配置**
   编辑 `send_message.py` 文件，将 `YOUR_WEBHOOK_KEY` 替换为实际的机器人 webhook key：
   ```bash
   vim send_message.py
   # 找到这行：WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY"
   # 替换为：WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的实际key"
   ```

3. **执行部署脚本**
   ```bash
   chmod +x deploy.sh
   sudo ./deploy.sh
   ```

## 手动部署步骤

### 第一步：安装依赖
```bash
# 安装 Python 依赖
pip3 install -r requirements.txt
# 依赖包包括：requests, schedule, pytz, logging
```

### 第二步：配置服务
```bash
# 创建部署目录
sudo mkdir -p /opt/wechat-robot

# 复制文件
sudo cp send_message.py /opt/wechat-robot/
sudo cp requirements.txt /opt/wechat-robot/

# 设置权限
sudo chown -R www-data:www-data /opt/wechat-robot

# 配置系统服务
sudo cp send_message.service /etc/systemd/system/
sudo systemctl daemon-reload
```

### 第三步：启动服务
```bash
# 启用开机自启
sudo systemctl enable send_message.service

# 启动服务
sudo systemctl start send_message.service
```

## 服务管理命令

### 查看服务状态
```bash
sudo systemctl status send_message.service
```

### 停止服务
```bash
sudo systemctl stop send_message.service
```

### 启动服务
```bash
sudo systemctl start send_message.service
```

### 重启服务
```bash
sudo systemctl restart send_message.service
```

### 查看实时日志
```bash
# 查看应用日志（程序运行目录下）
sudo tail -f /opt/wechat-robot/send_message.log

# 查看系统服务日志
sudo journalctl -u send_message.service -f

# 查看系统服务输出日志
sudo tail -f /var/log/send_message.log

# 查看系统服务错误日志
sudo tail -f /var/log/send_message_error.log
```

### 禁用服务（完全关闭）
```bash
sudo systemctl stop send_message.service
sudo systemctl disable send_message.service
```

## 配置说明

### 时间设置
- 程序已配置为北京时间每周一 00:01-00:10 随机时间自动发送消息
- 每周最多发送一次，避免重复发送
- 发送记录保存在 `send_record.json` 文件中
- 如需修改时间窗口，编辑 `send_message.py` 中的这个条件：
  ```python
  if not (beijing_now.hour == 0 and 1 <= beijing_now.minute <= 10):
  ```
- 可修改为其他时间窗口，如：
  ```python
  if not (beijing_now.hour == 9 and 0 <= beijing_now.minute <= 30):  # 9:00-9:30
  ```

### 消息内容修改
编辑 `send_message.py` 中的 `MESSAGE_CONTENT` 变量来修改发送的消息内容。

### 让机器人更像你的分身

1. **头像设置**
   - 在企业微信管理后台将机器人头像改为你的照片

2. **消息格式**
   - 程序使用简洁的纯文本格式
   - 保持与原始需求图片一致的消息内容和格式
   - 避免过于复杂的格式，更像真实的工作消息

3. **发送时间优化**
   - 使用随机时间发送（00:01-00:10），避免固定时间模式
   - 每周只发送一次，避免频繁发送引起注意

## 故障排除

### 服务无法启动
```bash
# 查看详细错误信息
sudo journalctl -u send_message.service -n 50

# 检查文件权限
ls -la /opt/wechat-robot/
```

### 消息发送失败
```bash
# 查看程序日志（程序运行目录）
sudo tail -f /opt/wechat-robot/send_message.log

# 查看系统服务错误日志
sudo tail -f /var/log/send_message_error.log

# 检查网络连接
curl -X POST "你的webhook地址" -H "Content-Type: application/json" -d '{"msgtype":"text","text":{"content":"测试消息"}}'

# 检查发送记录状态
cd /opt/wechat-robot
python3 test_send.py
```

### 时间不准确
程序已设置为北京时间，每10秒检查一次时间窗口，如果仍有问题：
```bash
# 检查服务器时区
timedatectl

# 设置服务器时区为上海（北京时间）
sudo timedatectl set-timezone Asia/Shanghai

# 重启服务以应用时区更改
sudo systemctl restart send_message.service
```

### 发送时间窗口问题
如果错过了发送时间窗口或需要测试：
```bash
# 进入程序目录
cd /opt/wechat-robot

# 清除本周发送记录，允许重新发送
python3 manage_records.py
# 选择选项 2

# 立即测试发送（不等待定时）
python3 test_send.py
```

### 检查程序运行状态
```bash
# 检查程序是否正在运行
sudo systemctl status send_message.service

# 检查程序检测时间窗口的频率（应该每10秒检查一次）
sudo journalctl -u send_message.service -f

# 查看当前发送记录状态
cd /opt/wechat-robot && python3 -c "
from send_message import should_send_this_week, get_week_key, load_send_record
from datetime import datetime
import pytz
beijing_now = datetime.now(pytz.timezone('Asia/Shanghai'))
print(f'当前时间: {beijing_now.strftime(\"%Y-%m-%d %H:%M:%S\")})
print(f'当前周: {get_week_key(beijing_now)}')
print(f'可以发送: {should_send_this_week()}')
print(f'发送记录: {load_send_record()}')
"
```

## 文件说明

### 核心文件
- **send_message.py** - 主程序，负责定时发送消息
- **requirements.txt** - Python依赖包列表
- **send_message.service** - systemd服务配置文件
- **deploy.sh** - 一键部署脚本

### 工具文件
- **test_send.py** - 测试工具，验证配置和查看发送状态
- **manage_records.py** - 发送记录管理工具

### 运行时文件
- **send_message.log** - 程序运行日志
- **send_record.json** - 发送记录文件（程序自动创建）

## 程序工作原理

1. **时间检测**: 程序每10秒检查一次当前北京时间
2. **时间窗口**: 在每周一00:01-00:10时间窗口内触发发送
3. **随机发送**: 在窗口内随机选择具体的发送时间点
4. **防重复**: 检查本周是否已发送，避免重复发送
5. **记录保存**: 发送成功后保存记录到JSON文件
6. **重试机制**: 发送失败时自动重试最多3次

## 发送记录管理

### 查看发送状态
```bash
# 使用测试脚本查看发送状态
python3 test_send.py

# 使用管理工具查看详细记录
python3 manage_records.py
```

### 手动重置发送记录
如果需要在本周重新发送消息：
```bash
# 运行记录管理工具
python3 manage_records.py
# 选择选项 2 清除本周发送记录
```

### 发送记录文件
- 发送记录保存在 `send_record.json` 文件中
- 记录格式：`{"2025-W25": {"sent_time": "2025-06-23 00:05:23", "timestamp": 1719072323.0}}`
- 自动清理超过4周的旧记录

## 新功能特性

### 🎯 随机时间发送
- **时间窗口**: 每周一北京时间 00:01-00:10
- **随机性**: 在时间窗口内随机选择发送时间
- **更自然**: 避免固定时间发送，更像人工操作

### 🔒 防重复发送
- **周级别控制**: 每周最多发送一次消息
- **记录持久化**: 发送记录保存在文件中，重启服务不丢失
- **自动清理**: 自动清理超过4周的旧记录

### 🛠️ 管理工具
- **test_send.py**: 测试配置和查看发送状态
- **manage_records.py**: 管理发送记录，支持清除操作

## 安全提醒

⚠️ **重要安全事项：**
1. **保护 webhook 地址**：不要将包含 key 的 webhook 地址分享到公开场所
2. **定期检查日志**：确保服务正常运行，没有异常访问
3. **限制服务器访问**：确保只有授权用户可以访问服务器
4. **备份配置**：定期备份配置文件和脚本
5. **发送记录安全**：`send_record.json` 文件包含发送历史，注意保护

## 常见问题 FAQ

### Q: 为什么消息没有在预期时间发送？
A: 检查以下几点：
- 服务是否正在运行：`sudo systemctl status send_message.service`
- 本周是否已经发送过：`python3 test_send.py`
- 服务器时区是否为北京时间：`timedatectl`

### Q: 如何立即测试发送功能？
A: 运行测试脚本：`python3 test_send.py`

### Q: 如何修改发送时间？
A: 编辑 `send_message.py` 中的时间窗口条件：
```python
if not (beijing_now.hour == 0 and 1 <= beijing_now.minute <= 10):
```

### Q: 如何在本周重新发送消息？
A: 使用管理工具清除本周记录：`python3 manage_records.py`

### Q: 消息内容如何修改？
A: 编辑 `send_message.py` 中的 `MESSAGE_CONTENT` 变量

### Q: 服务重启后会重复发送吗？
A: 不会，发送记录保存在文件中，重启后程序会检查记录避免重复发送
